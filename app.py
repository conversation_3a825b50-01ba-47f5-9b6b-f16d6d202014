# === PHISHING DETECTION APP ===
# Run this app: python app.py
# Make sure 'phishing_model.pkl' exists in the same directory!

from flask import Flask, render_template, request, jsonify, flash  # Web app tools
import pickle           # To load the saved ML model
import os               # File path handling
from urllib.parse import urlparse  # For breaking down URLs
import re               # Regex for validating URLs
import logging          # Logging info and errors
import json             # JSON handling
try:
    from openai import OpenAI  # Updated OpenAI API
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI not available. AI features will be disabled.")

# === Initialize Flask App ===
app = Flask(__name__)
# Use environment variable for secret key, fallback to default for development
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')

# === Setup Logging ===
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === AI Configuration ===
# OpenAI API configuration - Use environment variable for security
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', None)
openai_client = None

if OPENAI_AVAILABLE and OPENAI_API_KEY:
    try:
        openai_client = OpenAI(api_key=OPENAI_API_KEY)
        logger.info("OpenAI client initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize OpenAI client: {e}")
        openai_client = None
else:
    logger.warning("OpenAI API key not found. Set OPENAI_API_KEY environment variable to enable AI features.")

# === Global model variable (to store the loaded ML model) ===
model = None

# === Function: Load the model from a pickle file ===
def load_model():
    global model
    model_path = 'phishing_model.pkl'  # Ensure this file exists!

    try:
        if os.path.exists(model_path):
            # Suppress sklearn version warnings during model loading
            import warnings
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=UserWarning)
                with open(model_path, 'rb') as f:
                    model = pickle.load(f)

            # Test the model with a simple prediction to ensure it works
            try:
                test_features = [20, 0, 1, 1, 2]  # Simple test features
                test_prediction = model.predict([test_features])
                logger.info(f"Model loaded successfully and tested. Test prediction: {test_prediction[0]}")
            except Exception as test_error:
                logger.warning(f"Model loaded but test prediction failed: {test_error}")
                logger.info("Model may still work for actual predictions")

            return True
        else:
            logger.error(f"Model file {model_path} not found")
            return False
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return False

# === Function: Validate URL format ===
def validate_url(url):
    """
    Validate URL format and structure
    Returns True if valid, False otherwise
    """
    if not url or not isinstance(url, str):
        return False

    # Remove whitespace
    url = url.strip()

    # Check minimum length
    if len(url) < 4:
        return False

    # Add scheme if missing for parsing
    test_url = url
    if not url.startswith(('http://', 'https://')):
        test_url = 'http://' + url

    try:
        parsed = urlparse(test_url)

        # Must have a domain
        if not parsed.netloc:
            return False

        # Domain must contain at least one dot
        if '.' not in parsed.netloc:
            return False

        # Domain must not be just dots
        if parsed.netloc.replace('.', '') == '':
            return False

        # Basic domain format check
        domain_parts = parsed.netloc.split('.')
        for part in domain_parts:
            if not part:  # Empty part (consecutive dots)
                return False

        return True

    except Exception:
        return False

# === Function: Extract features from a URL ===
# These features are what the ML model uses to predict phishing
def extract_url_features(url):
    try:
        # Validate URL first
        if not validate_url(url):
            raise ValueError(f"Invalid URL format: {url}")

        # Add default scheme if missing
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url

        parsed_url = urlparse(url)  # Break the URL into parts

        # Extract simple numeric features
        url_length = len(url)
        at_count = url.count('@')
        dash_count = url.count('-')
        has_https = 1 if url.startswith('https://') else 0
        domain = parsed_url.netloc
        dot_count_in_domain = domain.count('.')

        features = [url_length, at_count, dash_count, has_https, dot_count_in_domain]

        logger.info(f"Extracted features for URL '{url}': {features}")
        return features

    except Exception as e:
        logger.error(f"Error extracting features from URL '{url}': {str(e)}")
        raise

# === Function: AI-powered URL analysis using OpenAI ===
def analyze_url_with_ai(url):
    """
    Use OpenAI API to analyze URL for phishing indicators
    Returns detailed analysis and confidence score
    """
    global openai_client

    # Check if OpenAI is available
    if not openai_client:
        logger.warning("OpenAI client not available. Returning fallback analysis.")
        return {
            "is_phishing": False,
            "confidence": 0,
            "reasons": ["AI analysis unavailable - OpenAI client not configured"],
            "recommendation": "Use traditional ML model results"
        }

    try:
        prompt = f"""
        Analyze this URL for phishing indicators: {url}

        Consider these factors:
        1. Domain legitimacy and spelling
        2. Suspicious subdomains or paths
        3. URL structure and patterns
        4. Known phishing techniques
        5. Brand impersonation attempts

        Respond with a JSON object containing:
        - "is_phishing": boolean (true if likely phishing)
        - "confidence": number (0-100, confidence percentage)
        - "reasons": array of strings (specific reasons for the assessment)
        - "recommendation": string (what user should do)

        Be thorough but concise in your analysis.
        """

        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a cybersecurity expert specializing in phishing detection. Analyze URLs and provide detailed security assessments."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.1
        )

        ai_response = response.choices[0].message.content

        # Try to parse JSON response
        try:
            ai_analysis = json.loads(ai_response)

            # Validate required fields and types
            if not isinstance(ai_analysis, dict):
                raise ValueError("AI response is not a dictionary")

            # Ensure required fields exist with proper types
            ai_analysis.setdefault('is_phishing', False)
            ai_analysis.setdefault('confidence', 50)
            ai_analysis.setdefault('reasons', ["AI analysis completed"])
            ai_analysis.setdefault('recommendation', "Manual verification recommended")

            # Validate types
            if not isinstance(ai_analysis['is_phishing'], bool):
                ai_analysis['is_phishing'] = bool(ai_analysis.get('is_phishing', False))

            if not isinstance(ai_analysis['confidence'], (int, float)):
                ai_analysis['confidence'] = 50
            else:
                # Ensure confidence is within valid range
                ai_analysis['confidence'] = max(0, min(100, ai_analysis['confidence']))

            if not isinstance(ai_analysis['reasons'], list):
                ai_analysis['reasons'] = ["AI analysis completed"]

            if not isinstance(ai_analysis['recommendation'], str):
                ai_analysis['recommendation'] = "Manual verification recommended"

        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.warning(f"AI response parsing error: {e}")
            # Fallback if AI doesn't return valid JSON
            ai_analysis = {
                "is_phishing": False,
                "confidence": 50,
                "reasons": ["AI analysis completed but format was unclear"],
                "recommendation": "Manual verification recommended"
            }

        logger.info(f"AI analysis completed for URL: {url}")
        return ai_analysis

    except Exception as e:
        logger.error(f"Error in AI analysis: {str(e)}")
        # Return safe fallback
        return {
            "is_phishing": False,
            "confidence": 0,
            "reasons": [f"AI analysis failed: {str(e)}"],
            "recommendation": "Use traditional ML model results"
        }

# === Function: Predict if the URL is phishing or legitimate (Enhanced with AI) ===
def predict_phishing(url, use_ai=True):
    global model, openai_client

    if model is None:
        raise Exception("Model not loaded")

    try:
        # Get traditional ML prediction
        features = extract_url_features(url)
        ml_prediction = model.predict([features])[0]

        # Get AI analysis if enabled and available
        ai_analysis = None
        if use_ai and openai_client:
            ai_analysis = analyze_url_with_ai(url)
        elif use_ai and not openai_client:
            logger.info("AI analysis requested but OpenAI client not available")

        # Combine ML and AI results
        if ai_analysis and ai_analysis.get('confidence', 0) > 70:
            # High confidence AI result - use AI prediction
            if ai_analysis['is_phishing']:
                final_prediction = "⚠️ Phishing (AI Detected)"
                confidence_class = "danger"
            else:
                final_prediction = "✅ Legitimate (AI Verified)"
                confidence_class = "success"
        else:
            # Use traditional ML prediction
            if ml_prediction == 1:
                final_prediction = "⚠️ Phishing"
                confidence_class = "danger"
            else:
                final_prediction = "✅ Legitimate"
                confidence_class = "success"

        return final_prediction, confidence_class, ai_analysis

    except Exception as e:
        logger.error(f"Error making prediction: {str(e)}")
        raise

# === Route: Homepage ===
@app.route('/')
def index():
    return render_template('index.html')  # Render main input page

# === Route: Handle form submission (HTML) ===
@app.route('/predict', methods=['POST'])
def predict():
    try:
        url = request.form.get('url', '').strip()  # Get user input

        if not url:
            flash('Please enter a URL', 'error')
            return render_template('index.html')

        # Validate URL format
        if not validate_url(url):
            flash('Please enter a valid URL', 'error')
            return render_template('index.html')

        prediction_text, confidence_class, ai_analysis = predict_phishing(url)

        return render_template('result.html',
                               url=url,
                               prediction=prediction_text,
                               confidence_class=confidence_class,
                               ai_analysis=ai_analysis)

    except Exception as e:
        logger.error(f"Error in prediction route: {str(e)}")
        flash(f'Error processing URL: {str(e)}', 'error')
        return render_template('index.html')

# === API Route: Accepts JSON POST requests for programmatic access ===
# Test using: curl -X POST http://localhost:5000/api/predict -H "Content-Type: application/json" -d '{"url":"http://example.com"}'
@app.route('/api/predict', methods=['POST'])
def api_predict():
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({'error': 'URL is required'}), 400

        url = data['url'].strip()

        if not url:
            return jsonify({'error': 'URL cannot be empty'}), 400

        # Validate URL format
        if not validate_url(url):
            return jsonify({'error': 'Invalid URL format'}), 400

        prediction_text, confidence_class, ai_analysis = predict_phishing(url)

        return jsonify({
            'url': url,
            'prediction': prediction_text,
            'confidence_class': confidence_class,
            'features': extract_url_features(url),
            'ai_analysis': ai_analysis
        })

    except Exception as e:
        logger.error(f"Error in API prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

# === API Route: AI-only analysis ===
@app.route('/api/ai-analyze', methods=['POST'])
def ai_analyze():
    """AI-powered URL analysis endpoint"""
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({'error': 'URL is required'}), 400

        url = data['url'].strip()

        if not url:
            return jsonify({'error': 'URL cannot be empty'}), 400

        # Validate URL format
        if not validate_url(url):
            return jsonify({'error': 'Invalid URL format'}), 400

        # Get AI analysis only
        ai_analysis = analyze_url_with_ai(url)

        return jsonify({
            'url': url,
            'ai_analysis': ai_analysis,
            'features': extract_url_features(url)
        })

    except Exception as e:
        logger.error(f"Error in AI analysis: {str(e)}")
        return jsonify({'error': str(e)}), 500

# === Start the Flask app ===
# Run this script from terminal: python app.py
# Access at: http://localhost:5000/
if __name__ == '__main__':
    if load_model():
        print("✅ Model loaded successfully. Starting Flask app...")
        app.run(debug=True, host='0.0.0.0', port=5000)  # Accessible locally and on network
    else:
        print("❌ Failed to load model. Please ensure 'phishing_model.pkl' exists in the project directory.")
        print("The app will not start without a valid model file.")
